import { z } from 'zod';
import { getEnvironmentConfig } from '../utils/env.js';

const GetOrderDetailArgsSchema = z.object({
  vid: z.string().describe('Order VID (required)'),
  orderNO: z.string().optional().describe('Order number (optional)'),
  ikeaOrderNO: z.string().optional().describe('IKEA order number (optional)'),
});

const QueryOrderListsArgsSchema = z.object({
  page: z.number().min(1).default(1).describe('Page number (starting from 1)'),
  pageSize: z.number().min(1).max(100).default(10).describe('Number of items per page (1-100)'),
  orderStatus: z.string().optional().describe('Filter by order status'),
  storeCode: z.string().optional().describe('Filter by store code'),
  startDate: z.string().optional().describe('Start date filter (YYYY-MM-DD)'),
  endDate: z.string().optional().describe('End date filter (YYYY-MM-DD)'),
});

const VidArgsSchema = z.object({
  vid: z.string().describe('Order VID (required)'),
});

const EmptyArgsSchema = z.object({});
const TestEchoArgsSchema = z.object({
  message: z.string().optional().describe('Message to echo back'),
  data: z.any().optional().describe('Data to echo back'),
});

function safeStringify(obj: any, indent: number = 0): string {
  const seen = new WeakSet();
  const replacer = (_key: string, value: any) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) return '[Circular Reference]';
      seen.add(value);
    }
    if (value instanceof Error) return { name: value.name, message: value.message, stack: value.stack };
    if (typeof value === 'function') return '[Function]';
    if (value === undefined) return '[Undefined]';
    return value;
  };

  try {
    return JSON.stringify(obj, replacer, indent);
  } catch (error) {
    return `[Object: ${typeof obj}]`;
  }
}

async function makeHttpRequest(url: string, method: string = 'POST', data?: any) {
  const env = getEnvironmentConfig();
  if (!env.AUTH_COOKIES) throw new Error('Authentication cookies are required');

  return await import('../utils/http.js').then(({ default: http }) =>
    http({ url, method, data, useKong: true, cookies: env.AUTH_COOKIES })
  );
}

export const SERVICE_MODULES = {
  oms: {
    queryOrderLists: async (data: any) => makeHttpRequest('/orders/search', 'POST', {
      page: data.page || 1,
      pageSize: data.pageSize || 10,
      orderStatus: data.orderStatus,
      storeCode: data.storeCode,
      startDate: data.startDate,
      endDate: data.endDate,
    }),

    getOrderDetail: async (data: any) => {
      const params = typeof data === 'string' ? { vid: data } : data;
      return makeHttpRequest('/order/detail', 'POST', {
        vid: params.vid,
        orderNO: params.orderNO,
        ikeaOrderNO: params.ikeaOrderNO,
      });
    },

    getLogisticsInfo: async (data: any) => makeHttpRequest(`/logistics/${data.vid}`, 'GET'),
    getAssemblingInfo: async (data: any) => makeHttpRequest(`/assembling/${data.vid}`, 'GET'),
    searchPermissionStore: async () => makeHttpRequest('/user/searchPermissionStore', 'GET'),
    getRetrievalData: async (data: any) => makeHttpRequest(`/order/${data.vid}/retrieval`, 'GET'),
  },

  global: {
    getCurrentUser: async () => makeHttpRequest('/user/current', 'GET'),
  },

  test: {
    ping: async (data: any) => ({
      success: true,
      message: 'pong',
      timestamp: new Date().toISOString(),
      data: data || null,
    }),

    echo: async (data: any) => ({
      success: true,
      message: 'echo response',
      timestamp: new Date().toISOString(),
      echo: data,
    }),
  },
};

/**
 * 🔧 工具配置
 */
export const SERVICE_TOOL_CONFIGS = {
  oms: [
    {
      name: 'queryOrderLists',
      description: 'Query order lists from OMS system',
      zodSchema: QueryOrderListsArgsSchema,
    },
    {
      name: 'getOrderDetail',
      description: 'Get detailed information for a specific order',
      zodSchema: GetOrderDetailArgsSchema,
    },
    {
      name: 'getLogisticsInfo',
      description: 'Get logistics information for an order',
      zodSchema: VidArgsSchema,
    },
    {
      name: 'getAssemblingInfo',
      description: 'Get assembling information for an order',
      zodSchema: VidArgsSchema,
    },
    {
      name: 'searchPermissionStore',
      description: 'Search permission stores for current user',
      zodSchema: EmptyArgsSchema,
    },
    {
      name: 'getRetrievalData',
      description: 'Get retrieval data for an order',
      zodSchema: VidArgsSchema,
    },
  ],

  global: [
    {
      name: 'getCurrentUser',
      description: 'Get current user information',
      zodSchema: EmptyArgsSchema,
    },
  ],

  test: [
    {
      name: 'ping',
      description: 'Quick ping test - returns immediately',
      zodSchema: EmptyArgsSchema,
    },
    {
      name: 'echo',
      description: 'Echo test - returns input data',
      zodSchema: TestEchoArgsSchema,
    },
  ],
};

function createServiceToolHandler(moduleName: string, functionName: string, zodSchema?: any) {
  return async (args: any) => {
    const requestId = Math.random().toString(36).substring(2, 15);
    const timestamp = new Date().toISOString();

    try {
      let validatedArgs = args;
      if (zodSchema) {
        try {
          validatedArgs = zodSchema.parse(args);
        } catch (zodError: any) {
          return {
            success: false,
            error: 'Invalid arguments provided',
            details: { validationErrors: zodError.errors, receivedArgs: args },
            meta: { module: moduleName, function: functionName, requestId },
          };
        }
      }

      const serviceModule = (SERVICE_MODULES as any)[moduleName];
      if (!serviceModule) throw new Error(`Service module '${moduleName}' not found`);

      const serviceFunction = serviceModule[functionName];
      if (!serviceFunction) throw new Error(`Function '${functionName}' not found in module '${moduleName}'`);

      const functionArgs = validatedArgs.data !== undefined ? validatedArgs.data :
        (() => { const args = { ...validatedArgs }; delete args.cookies; return args; })();

      const result = await serviceFunction(functionArgs);

      return {
        success: true,
        data: result,
        meta: { module: moduleName, function: functionName, requestId, timestamp },
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        details: { stack: error.stack, name: error.name },
        meta: { module: moduleName, function: functionName, requestId, timestamp },
      };
    }
  };
}

function serializeResult(result: any): string {
  try {
    return safeStringify(result, 2);
  } catch (jsonError) {
    return safeStringify({
      success: result?.success || false,
      error: result?.error || 'Serialization failed',
      meta: result?.meta || {},
    }, 2);
  }
}

export function registerAllServiceTools(server: any) {
  let totalTools = 0;

  Object.entries(SERVICE_TOOL_CONFIGS).forEach(([moduleName, functions]) => {
    functions.forEach(func => {
      const toolName = `${moduleName}_${func.name}`;
      const handler = createServiceToolHandler(moduleName, func.name, (func as any).zodSchema);
      const zodSchema = (func as any).zodSchema;

      const toolHandler = async (args: any) => {
        const result = await handler(args);
        return { content: [{ type: 'text', text: serializeResult(result) }] };
      };

      try {
        if (zodSchema) {
          server.tool(toolName, func.description, zodSchema, toolHandler);
        } else {
          server.tool(toolName, func.description, toolHandler);
        }
      } catch (error: any) {
        console.error(`Failed to register tool: ${toolName} - ${error.message}`);
      }
    });
  });

  console.error(`Registered ${totalTools} service tools`);
  return totalTools;
}