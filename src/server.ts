import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ToolSchema,
} from "@modelcontextprotocol/sdk/types.js";
import { z } from "zod";
import { zodToJsonSchema } from "zod-to-json-schema";
import { SERVICE_MODULES, SERVICE_TOOL_CONFIGS } from "./adapters/service-adapter.js";

type ToolInput = z.infer<typeof ToolSchema.shape.inputSchema>;

// Safe JSON stringify function to handle circular references
function safeStringify(obj: any, indent: number = 0): string {
  const seen = new WeakSet();

  const replacer = (_key: string, value: any) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return '[Circular Reference]';
      }
      seen.add(value);
    }

    // Handle special object types
    if (value instanceof Error) {
      return {
        name: value.name,
        message: value.message,
        stack: value.stack,
      };
    }

    // Handle functions
    if (typeof value === 'function') {
      return '[Function]';
    }

    // Handle undefined
    if (value === undefined) {
      return '[Undefined]';
    }

    // Handle HTTP request/response objects
    if (value && typeof value === 'object') {
      // Skip problematic HTTP-related properties
      if (value.constructor && (
        value.constructor.name === 'ClientRequest' ||
        value.constructor.name === 'IncomingMessage' ||
        value.constructor.name === 'Agent' ||
        value.constructor.name === 'TLSSocket'
      )) {
        return `[${value.constructor.name}]`;
      }
    }

    return value;
  };

  try {
    return JSON.stringify(obj, replacer, indent);
  } catch (error) {
    // If still fails, return a safe string representation
    return `[Object: ${typeof obj}]`;
  }
}

// 所有业务逻辑现在都在 service-adapter.ts 中实现

export function createServer(): Server {
  const server = new Server(
    {
      name: "orders-portal-mcp-server",
      version: "0.1.0",
    },
    {
      capabilities: {
        tools: {},
      },
    }
  );

  // List tools handler - 使用 service-adapter 的配置
  server.setRequestHandler(ListToolsRequestSchema, async () => {
    const tools: any[] = [];

    Object.entries(SERVICE_TOOL_CONFIGS).forEach(([moduleName, functions]) => {
      functions.forEach(func => {
        const toolName = `${moduleName}_${func.name}`;

        const toolDef: any = {
          name: toolName,
          description: func.description,
        };

        // 如果有 Zod schema，转换为 JSON schema
        if ((func as any).zodSchema) {
          toolDef.inputSchema = zodToJsonSchema((func as any).zodSchema) as ToolInput;
        }

        tools.push(toolDef);
      });
    });

    return { tools };
  });

  // Call tool handler - 使用 service-adapter 的通用处理器
  server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const { name, arguments: args } = request.params;

    try {
      // 解析工具名称
      const [moduleName, functionName] = name.split('_', 2);

      // 获取服务模块
      const serviceModule = (SERVICE_MODULES as any)[moduleName];
      if (!serviceModule) {
        throw new Error(`Service module '${moduleName}' not found`);
      }

      // 获取服务函数
      const serviceFunction = serviceModule[functionName];
      if (!serviceFunction) {
        throw new Error(`Function '${functionName}' not found in module '${moduleName}'`);
      }

      // 获取对应的 Zod schema 进行验证
      const moduleConfig = (SERVICE_TOOL_CONFIGS as any)[moduleName];
      const functionConfig = moduleConfig?.find((f: any) => f.name === functionName);

      let validatedArgs = args;
      if (functionConfig?.zodSchema) {
        validatedArgs = functionConfig.zodSchema.parse(args);
      }

      // 调用服务函数
      const result = await serviceFunction(validatedArgs);

      return {
        content: [
          {
            type: "text",
            text: safeStringify(result, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  });

  return server;
}
