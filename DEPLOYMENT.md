# 🚀 Orders Portal MCP Server Deployment Guide

This guide covers deploying the Orders Portal MCP Server using Docker in various environments.

## 📋 Prerequisites

- Docker 20.10+ and Docker Compose 2.0+
- Access to IKEA internal networks (for production deployment)
- Valid OAuth2 credentials from Keycloak
- Environment-specific configuration values

## 🔧 Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd mcp-server
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit with your actual values (NEVER commit .env to git!)
nano .env
```

> ⚠️ **Security Note**: Never commit `.env` files with secrets to version control!
> See [Environment Configuration Guide](./docs/ENVIRONMENT_CONFIGURATION.md) for detailed setup.

### 3. Deploy

```bash
# Development (with hot reload)
./scripts/deploy.sh development up

# Production
./scripts/deploy.sh production up
```

## 🌍 Environment Configuration

### Required Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `NODE_ENV` | Application environment | No | `production` |
| `MCP_SERVER_PORT` | Server port | No | `3000` |
| `MCP_SERVER_BASE_URL` | Server base URL | Yes | - |
| `VITE_API_HOST_KONG` | Kong API gateway URL | Yes | - |
| `OAUTH2_CLIENT_SECRET` | OAuth2 client secret | Yes* | - |
| `AUTH_COOKIES` | Auth cookies (if OAuth2 disabled) | Yes* | - |

*Required based on authentication method

### OAuth2 Configuration

When `OAUTH2_ENABLED=true` (recommended for production):

```env
OAUTH2_ENABLED=true
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn
KEYCLOAK_REALM=master
OAUTH2_CLIENT_ID=mcp-server
OAUTH2_CLIENT_SECRET=your-secret-here
OAUTH2_REDIRECT_URI=https://your-domain.com/auth/callback
OAUTH2_SCOPES=openid,profile,email
```

### Cookie-Based Authentication

When `OAUTH2_ENABLED=false` (development only):

```env
OAUTH2_ENABLED=false
AUTH_COOKIES=your-auth-cookies-here
```

## 🐳 Docker Deployment

### Using Docker Compose (Recommended)

#### Development Environment
```bash
# Start with hot reload
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# View logs
docker-compose logs -f mcp-server
```

#### Production Environment
```bash
# Build and start
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Check status
docker-compose ps
```

### Using Deployment Script

The deployment script provides a convenient interface:

```bash
# Show help
./scripts/deploy.sh --help

# Development deployment
./scripts/deploy.sh development up

# Production deployment
./scripts/deploy.sh production up

# View logs
./scripts/deploy.sh production logs

# Check status
./scripts/deploy.sh production status

# Clean up
./scripts/deploy.sh production clean
```

### Manual Docker Commands

```bash
# Build image
docker build -t orders-portal-mcp-server .

# Run container
docker run -d \
  --name mcp-server \
  -p 3000:3000 \
  --env-file .env \
  orders-portal-mcp-server
```

## 🏗️ Deployment Environments

### Development
- Hot reload enabled
- Debug logging enabled
- Relaxed authentication
- Local file mounting

### Production
- Optimized build
- Security hardening
- Strict authentication
- Resource limits
- Health checks

### Test
- Base configuration
- Test API endpoints enabled
- Moderate security

## 🔒 Security Considerations

### Production Security Checklist

- [ ] `OAUTH2_ENABLED=true`
- [ ] `CONNECTION_AUTH_ENABLED=true`
- [ ] `CONNECTION_AUTH_STRICT=true`
- [ ] `CONNECTION_AUTH_TEST_API=false`
- [ ] `DEBUG_SERVICE_ADAPTER=false`
- [ ] Strong `OAUTH2_CLIENT_SECRET`
- [ ] HTTPS URLs for all endpoints
- [ ] Internal network deployment
- [ ] Resource limits configured
- [ ] Health checks enabled

### Network Security

For production deployment, consider:

1. **Internal Network Only**: Deploy to `admin.ingka-internal.cn`
2. **VPN Access**: Require VPN for external access
3. **Firewall Rules**: Restrict access to authorized IPs
4. **TLS Termination**: Use reverse proxy with TLS

## 📊 Monitoring and Health Checks

### Health Check Endpoint

```bash
curl http://localhost:3000/health
```

Response:
```json
{
  "status": "healthy",
  "transport": "streamable-http",
  "timestamp": "2025-01-01T00:00:00.000Z",
  "activeSessions": 0,
  "sessions": []
}
```

### Docker Health Checks

Built-in Docker health checks monitor:
- HTTP endpoint availability
- Response time
- Service status

### Logging

Logs are structured and include:
- Request/response logging
- Authentication events
- Error tracking
- Performance metrics

## 🔧 Troubleshooting

### Common Issues

#### 1. Authentication Failures
```bash
# Check OAuth2 configuration
curl http://localhost:3000/auth/status

# Verify Keycloak connectivity
curl https://keycloak.ingka-dt.cn/auth/realms/master/.well-known/openid-configuration
```

#### 2. API Connection Issues
```bash
# Test API connectivity
docker exec mcp-server curl -I https://fe-dev-i.ingka-dt.cn

# Check environment variables
docker exec mcp-server env | grep VITE_API
```

#### 3. Container Startup Issues
```bash
# Check container logs
docker logs mcp-server

# Verify environment configuration
docker exec mcp-server node -e "console.log(process.env)"
```

### Debug Mode

Enable debug logging:
```env
DEBUG_SERVICE_ADAPTER=true
PER_REQUEST_AUTH_LOG_VALIDATION=true
```

## 📈 Scaling and Performance

### Resource Requirements

| Environment | CPU | Memory | Storage |
|-------------|-----|--------|---------|
| Development | 0.25 | 128MB | 1GB |
| Test | 0.5 | 256MB | 2GB |
| Production | 1.0 | 512MB | 5GB |

### Horizontal Scaling

For high availability:

1. **Load Balancer**: Use nginx or cloud load balancer
2. **Multiple Instances**: Run multiple containers
3. **Session Affinity**: Configure sticky sessions if needed
4. **Health Checks**: Ensure proper health check configuration

## 🔄 Updates and Maintenance

### Rolling Updates

```bash
# Build new version
docker build -t orders-portal-mcp-server:v1.1.0 .

# Update docker-compose.yml with new version
# Then restart
docker-compose up -d
```

### Backup and Recovery

Important files to backup:
- `.env` configuration
- `logs/` directory
- Docker volumes (if used)

## 📞 Support

For deployment issues:
1. Check this documentation
2. Review container logs
3. Verify environment configuration
4. Contact the development team

## 🔗 Related Documentation

- [README.md](./README.md) - General project information
- [docs/AUTHENTICATION_ARCHITECTURE.md](./docs/AUTHENTICATION_ARCHITECTURE.md) - Authentication details
- [docs/oauth2.md](./docs/oauth2.md) - OAuth2 configuration
- [docs/testing.md](./docs/testing.md) - Testing guide
