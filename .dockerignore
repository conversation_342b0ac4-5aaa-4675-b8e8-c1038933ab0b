# 🐳 Docker ignore file for Orders Portal MCP Server

# ================================
# Node.js
# ================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ================================
# Build outputs
# ================================
dist/
*.tsbuildinfo

# ================================
# Environment and configuration
# ================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ================================
# IDE and editor files
# ================================
.vscode/
.idea/
*.swp
*.swo
*~

# ================================
# OS generated files
# ================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ================================
# Git
# ================================
.git/
.gitignore

# ================================
# Docker
# ================================
Dockerfile*
.dockerignore
docker-compose*.yml

# ================================
# Documentation
# ================================
README.md
*.md
docs/

# ================================
# Testing (exclude from production builds)
# ================================
coverage/
.nyc_output/

# ================================
# Logs
# ================================
logs/
*.log

# ================================
# Runtime data
# ================================
pids/
*.pid
*.seed
*.pid.lock

# ================================
# Optional npm cache directory
# ================================
.npm

# ================================
# Optional eslint cache
# ================================
.eslintcache

# ================================
# Microbundle cache
# ================================
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ================================
# Optional REPL history
# ================================
.node_repl_history

# ================================
# Output of 'npm pack'
# ================================
*.tgz

# ================================
# Yarn Integrity file
# ================================
.yarn-integrity

# ================================
# parcel-bundler cache
# ================================
.cache
.parcel-cache

# ================================
# Next.js build output
# ================================
.next

# ================================
# Nuxt.js build / generate output
# ================================
.nuxt
dist

# ================================
# Gatsby files
# ================================
.cache/
public

# ================================
# Storybook build outputs
# ================================
.out
.storybook-out

# ================================
# Temporary folders
# ================================
tmp/
temp/
