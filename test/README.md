# MCP Server Test Suite

This directory contains a minimal, focused test suite for the Orders Portal MCP Server.

## Test Files

### 🚀 Core Tests (3 files)

1. **`quick-test.js`** - Quick health check and response time test
   - Tests basic MCP server functionality
   - Verifies response times for timeout diagnosis
   - Tests both local tools (ping/echo) and network tools (API calls)
   - **Usage**: `npm run build && node test/quick-test.js`

2. **`all-tools.js`** - Comprehensive test of all MCP tools
   - Tests all registered MCP tools with real parameters
   - Validates tool functionality and error handling
   - Generates detailed performance and success reports
   - **Usage**: `npm run build && node test/all-tools.js`

3. **`auth-cookies.js`** - Authentication verification test
   - Verifies AUTH_COOKIES are correctly transmitted
   - Tests HTTP client authentication mechanisms
   - Validates API access with session cookies
   - **Usage**: `npm run build && node test/auth-cookies.js`

## Test Scripts (package.json)

```bash
# Build and run basic server test
npm run test

# Quick health check (recommended for regular testing)
npm run build && node test/quick-test.js

# Test authentication cookies
npm run build && node test/auth-cookies.js

# Test all MCP tools comprehensively
npm run build && node test/all-tools.js
```

## Test Requirements

### Environment Setup
- **Required**: Valid `AUTH_COOKIES` in `.env` file for API tests
- **Optional**: `DEBUG_SERVICE_ADAPTER=true` for detailed logging

### Expected Results
- **Fast tools** (test_ping, test_echo): < 100ms
- **Network tools** (API calls): 100-500ms
- **Success rate**: 90-100% with valid authentication

## Test Coverage

✅ **Functionality** - All MCP tools work as expected  
✅ **Authentication** - Cookies transmitted correctly  
✅ **Performance** - Response time monitoring  
✅ **Error Handling** - Graceful failure modes  
✅ **Integration** - End-to-end API communication  

## Troubleshooting

### Common Issues

1. **Import Errors**: Run `npm run build` before testing
2. **Auth Failures**: Update `AUTH_COOKIES` in `.env` file
3. **Slow Responses**: Check network connectivity and API endpoints
4. **Tool Not Found**: Verify tool is registered in `SERVICE_TOOL_CONFIGS`

### Test Failures

- **401 Errors**: Authentication cookies expired or invalid
- **403 Errors**: User lacks required permissions
- **Network Errors**: API endpoints unreachable
- **Timeout Errors**: Responses taking > 30 seconds

## Removed Files

The following files were removed during cleanup:

- `keycloak-token-exchange.js` (453 lines) - OAuth2/Keycloak token exchange tests
- `keycloak-token-utils.js` (315 lines) - Keycloak utility functions
- `run-keycloak-tests.js` (191 lines) - Keycloak test runner

**Note**: OAuth2 functionality is still available. See [docs/oauth2.md](../docs/oauth2.md) for setup.

## Test Philosophy

This test suite follows a **minimal but comprehensive** approach:

- **3 focused test files** instead of 6 complex ones
- **Direct service function testing** instead of outdated wrapper functions
- **Clear, actionable output** with performance metrics
- **Real-world test scenarios** with actual API calls
- **Easy maintenance** with simple, readable code

## Quick Start

```bash
# 1. Ensure server builds successfully
npm run build

# 2. Run quick health check
node test/quick-test.js

# 3. If all tests pass, run comprehensive test
node test/all-tools.js

# 4. For auth-specific testing
node test/auth-cookies.js
```

This should give you confidence that your MCP server is working correctly!
