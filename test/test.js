#!/usr/bin/env node

/**
 * 🧪 Unified Test Suite
 * 
 * Simplified testing that covers all functionality:
 * - Authentication (OAuth2 + Cookie)
 * - MCP Tools
 * - Performance
 * - Error handling
 */

import { authenticate, validateToken, validateCookie, loadAuthConfig, clearAuthCache } from '../dist/auth/auth.js';

// Test configuration
const TEST_CONFIG = {
  serverUrl: 'http://localhost:6300',
  timeout: 10000,
  validCookie: 'OTJmNjk3MGItZGY5My00MzM3LWE4YjItMTZiZTczYjViZTc5QDE3NTM0MjYwOTAzNjIuM2QyZTI2NWMtZjg5NS00ZTMyLWIzYmUtNWExMzlmYTg1NmMxLjM2MDA='
};

// Test results
let results = {
  total: 0,
  passed: 0,
  failed: 0,
  tests: []
};

/**
 * Test runner utility
 */
async function runTest(name, testFn) {
  results.total++;
  const startTime = Date.now();
  
  try {
    await testFn();
    const duration = Date.now() - startTime;
    console.log(`✅ ${name} (${duration}ms)`);
    results.passed++;
    results.tests.push({ name, status: 'PASS', duration });
  } catch (error) {
    const duration = Date.now() - startTime;
    console.log(`❌ ${name} (${duration}ms): ${error.message}`);
    results.failed++;
    results.tests.push({ name, status: 'FAIL', duration, error: error.message });
  }
}

/**
 * Mock OAuth2 provider for testing
 */
function createMockProvider() {
  return {
    verifyAccessToken: async (token) => {
      await new Promise(resolve => setTimeout(resolve, 10)); // Simulate network delay
      
      if (token === 'valid-token') {
        return {
          token,
          clientId: 'permission-service',
          scopes: ['profile', 'roles', 'email'],
          expiresAt: Math.floor(Date.now() / 1000) + 3600,
          extra: {
            userInfo: {
              sub: 'test-user',
              azp: 'permission-service'
            }
          }
        };
      }
      throw new Error('Invalid token');
    }
  };
}

/**
 * Create mock request
 */
function createMockRequest(options = {}) {
  return {
    headers: {
      authorization: options.token ? `Bearer ${options.token}` : undefined,
      cookie: options.cookie ? `test_orders-portal=${options.cookie}` : undefined,
      'user-agent': 'Test/1.0.0'
    },
    method: 'POST',
    path: '/mcp',
    body: { method: 'tools/list' }
  };
}

/**
 * HTTP request utility
 */
async function httpRequest(url, options = {}) {
  const response = await fetch(url, {
    method: options.method || 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    body: options.body ? JSON.stringify(options.body) : undefined
  });
  
  const text = await response.text();
  let data;
  try {
    data = JSON.parse(text);
  } catch {
    data = text;
  }
  
  return { status: response.status, data, headers: response.headers };
}

/**
 * Test authentication configuration
 */
async function testAuthConfig() {
  const config = loadAuthConfig();
  
  if (!config.perRequestAuthEnabled) {
    throw new Error('Per-request auth should be enabled');
  }
  
  if (!config.cacheEnabled) {
    throw new Error('Cache should be enabled');
  }
}

/**
 * Test OAuth2 token validation
 */
async function testOAuth2Validation() {
  const provider = createMockProvider();
  const request = createMockRequest({ token: 'valid-token' });
  
  const result = await validateToken(request, provider);
  
  if (!result.success) {
    throw new Error(`OAuth2 validation failed: ${result.error}`);
  }
  
  if (!result.authInfo?.clientId) {
    throw new Error('Missing client ID in auth info');
  }
}

/**
 * Test cookie validation
 */
async function testCookieValidation() {
  const request = createMockRequest({ cookie: TEST_CONFIG.validCookie });
  
  const result = await validateCookie(request);
  
  // Note: This might fail if cookie is expired, which is expected
  if (!result.success && !result.error?.includes('expired')) {
    throw new Error(`Cookie validation failed unexpectedly: ${result.error}`);
  }
}

/**
 * Test token caching performance
 */
async function testTokenCaching() {
  clearAuthCache();
  
  const provider = createMockProvider();
  const request = createMockRequest({ token: 'valid-token' });
  
  // First request (no cache)
  const result1 = await validateToken(request, provider);
  if (!result1.success || result1.cached) {
    throw new Error('First request should succeed and not be cached');
  }
  
  // Second request (should be cached)
  const result2 = await validateToken(request, provider);
  if (!result2.success || !result2.cached) {
    throw new Error('Second request should succeed and be cached');
  }
  
  if (result2.duration >= result1.duration) {
    throw new Error('Cached request should be faster');
  }
}

/**
 * Test error handling
 */
async function testErrorHandling() {
  const provider = createMockProvider();
  
  // Test missing token
  const noTokenRequest = createMockRequest();
  const noTokenResult = await validateToken(noTokenRequest, provider);
  if (noTokenResult.success) {
    throw new Error('Should fail with missing token');
  }
  
  // Test invalid token
  const invalidTokenRequest = createMockRequest({ token: 'invalid-token' });
  const invalidTokenResult = await validateToken(invalidTokenRequest, provider);
  if (invalidTokenResult.success) {
    throw new Error('Should fail with invalid token');
  }
}

/**
 * Test MCP tools (if server is running)
 */
async function testMCPTools() {
  try {
    // Test ping tool
    const pingResponse = await httpRequest(`${TEST_CONFIG.serverUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Cookie': `test_orders-portal=${TEST_CONFIG.validCookie}`
      },
      body: {
        jsonrpc: '2.0',
        method: 'tools/call',
        params: {
          name: 'test_ping',
          arguments: {}
        },
        id: 1
      }
    });
    
    if (pingResponse.status !== 200 && pingResponse.status !== 401) {
      throw new Error(`Unexpected ping response status: ${pingResponse.status}`);
    }
    
    // Test tools list
    const toolsResponse = await httpRequest(`${TEST_CONFIG.serverUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Cookie': `test_orders-portal=${TEST_CONFIG.validCookie}`
      },
      body: {
        jsonrpc: '2.0',
        method: 'tools/list',
        id: 2
      }
    });
    
    if (toolsResponse.status !== 200 && toolsResponse.status !== 401) {
      throw new Error(`Unexpected tools list response status: ${toolsResponse.status}`);
    }
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      throw new Error('Server not running - start with "npm run dev"');
    }
    throw error;
  }
}

/**
 * Test server health
 */
async function testServerHealth() {
  try {
    const response = await httpRequest(`${TEST_CONFIG.serverUrl}/health`);
    
    if (response.status !== 200) {
      throw new Error(`Health check failed: ${response.status}`);
    }
    
    if (!response.data.status || response.data.status !== 'healthy') {
      throw new Error('Server not healthy');
    }
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      throw new Error('Server not running - start with "npm run dev"');
    }
    throw error;
  }
}

/**
 * Test auth status endpoint
 */
async function testAuthStatus() {
  try {
    const response = await httpRequest(`${TEST_CONFIG.serverUrl}/auth/status`);
    
    if (response.status !== 200) {
      throw new Error(`Auth status failed: ${response.status}`);
    }
    
    if (!response.data.config) {
      throw new Error('Missing auth config in response');
    }
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      throw new Error('Server not running - start with "npm run dev"');
    }
    throw error;
  }
}

/**
 * Main test runner
 */
async function runAllTests() {
  console.log('🧪 ==================== UNIFIED TEST SUITE ====================');
  console.log('🎯 Testing: Authentication, MCP Tools, Performance, Error Handling\n');
  
  // Unit tests (no server required)
  console.log('📋 Unit Tests:');
  await runTest('Auth Configuration', testAuthConfig);
  await runTest('OAuth2 Validation', testOAuth2Validation);
  await runTest('Cookie Validation', testCookieValidation);
  await runTest('Token Caching', testTokenCaching);
  await runTest('Error Handling', testErrorHandling);
  
  console.log('\n📡 Integration Tests (require running server):');
  await runTest('Server Health', testServerHealth);
  await runTest('Auth Status', testAuthStatus);
  await runTest('MCP Tools', testMCPTools);
  
  // Results summary
  console.log('\n🏁 ==================== TEST RESULTS ====================');
  console.log(`📊 Total: ${results.total}, Passed: ${results.passed}, Failed: ${results.failed}`);
  
  if (results.failed > 0) {
    console.log('\n❌ Failed Tests:');
    results.tests.filter(t => t.status === 'FAIL').forEach(test => {
      console.log(`   • ${test.name}: ${test.error}`);
    });
  }
  
  const avgDuration = results.tests.reduce((sum, t) => sum + t.duration, 0) / results.tests.length;
  console.log(`\n⚡ Average test duration: ${avgDuration.toFixed(1)}ms`);
  
  if (results.failed === 0) {
    console.log('\n🎉 All tests passed!');
  } else {
    console.log(`\n⚠️ ${results.failed} test(s) failed`);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  });
}

export { runAllTests };
