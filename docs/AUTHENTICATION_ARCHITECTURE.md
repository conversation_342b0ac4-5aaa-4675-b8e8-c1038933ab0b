# 🏗️ Authentication Architecture Design

## Overview

This document describes the authentication architecture for the Orders Portal MCP Server system.

## Components

```
A (MCP Inspector) → B (MCP Server) → C (Order Service)
```

- **A**: MCP Client (MCP Inspector)
- **B**: MCP Server (orders-portal-mcp-server)
- **C**: Order Service API (fe-dev-i.ingka-dt.cn/order-web)

## Authentication Flow

### 1. A → B: Keycloak OAuth2 Authentication

- **Protocol**: OAuth2 with Keycloak
- **Client**: `permission-service`
- **Flow**: Authorization Code or Client Credentials
- **Token**: JWT Bearer token in Authorization header
- **Purpose**: Authenticate MCP Inspector to MCP Server

**Configuration:**
```bash
OAUTH2_ENABLED=true
OAUTH2_CLIENT_ID=permission-service
OAUTH2_CLIENT_SECRET=cscxxxx
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn/auth
KEYCLOAK_REALM=master
```

**Request Format:**
```
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2. B: JWT Token Local Validation

- **Method**: Local JWT validation (not introspection)
- **Validation**: Signature, expiration, issuer, audience
- **Keys**: Use Keycloak public keys for verification
- **Purpose**: Verify A's token without calling Keycloak

**Implementation Status**: ✅ **Implemented**

### 3. B: Cookie Acquisition (To Be Implemented)

- **Input**: Valid JWT token from A
- **Output**: `test_orders-portal` cookie
- **Method**: TBD (implementation details needed)
- **Purpose**: Get session cookie for Order Service API

**Implementation Status**: ❌ **Not Implemented** - Awaiting implementation details

### 4. B → C: Cookie-Based Authentication

- **Method**: HTTP Cookie (`test_orders-portal`)
- **Format**: Base64-encoded pm_user_token
- **Purpose**: Order Service handles both authn and authz
- **Flow**: C validates cookie and extracts user permissions

**Cookie Format:**
```
Cookie: test_orders-portal=OTJmNjk3MGItZGY5My00MzM3LWE4YjI...
```

**Implementation Status**: ✅ **Implemented**

## Authentication Sequence Diagram

```
1. MCP Inspector → Keycloak: OAuth2 Authorization Request
2. Keycloak → MCP Inspector: Authorization Code
3. MCP Inspector → Keycloak: Exchange Code for JWT Token
4. Keycloak → MCP Inspector: JWT Bearer Token
5. MCP Inspector → MCP Server: MCP Initialize + Bearer Token
6. MCP Server: Local JWT Validation
7. MCP Server: Get Cookie (TBD Implementation)
8. MCP Server → Order Service: API Request + test_orders-portal Cookie
9. Order Service: Cookie Validation & Authorization
10. Order Service → MCP Server: API Response
11. MCP Server → MCP Inspector: MCP Response
```

## Current Implementation Status

| Component | Status | Notes |
|-----------|--------|-------|
| **A → B OAuth2** | ✅ **Implemented** | Keycloak with permission-service client |
| **B JWT Validation** | ✅ **Implemented** | Local validation with public keys |
| **B Cookie Acquisition** | ❌ **Not Implemented** | Awaiting implementation details |
| **B → C Cookie Auth** | ✅ **Implemented** | Cookie forwarding to Order Service |

## Configuration

### OAuth2 Configuration
```bash
# A → B: OAuth2 Configuration
OAUTH2_ENABLED=true
OAUTH2_CLIENT_ID=permission-service
OAUTH2_CLIENT_SECRET=cscxxxx
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn/auth
KEYCLOAK_REALM=master
```

### Connection Authentication Configuration
```bash
# B: Connection Authentication
CONNECTION_AUTH_ENABLED=true
CONNECTION_AUTH_STRICT=true
CONNECTION_AUTH_TEST_API=true
```

## Security Boundaries

1. **A → B Security**: OAuth2 + JWT validation ensures only authorized MCP clients
2. **B Internal**: JWT → Cookie conversion (implementation needed)
3. **B → C Security**: Cookie-based session authentication handled by Order Service

## Token Formats

### JWT Bearer Token (A → B)
- **Header**: `Authorization: Bearer <jwt-token>`
- **Validation**: Local validation using Keycloak public keys
- **Claims**: Standard OAuth2 claims (sub, iss, aud, exp, etc.)

### Cookie (B → C)
- **Header**: `Cookie: test_orders-portal=<base64-encoded-token>`
- **Format**: Base64-encoded pm_user_token
- **Structure**: `<EMAIL>` (Base64 encoded)

## Implementation Files

### Core Authentication Files
- `src/auth/connection-auth.ts` - Connection-time authentication validation
- `src/auth/middleware.js` - Authentication middleware for requests
- `src/auth/oauth-config.ts` - OAuth2 configuration and validation
- `src/transport/http-transport.ts` - HTTP transport with authentication integration

### Configuration Files
- `.env` - Environment configuration
- `src/utils/env.ts` - Environment variable handling

### Test Files
- `test/connection-auth-test.js` - Connection authentication tests
- `test/auth-cookies.js` - Cookie authentication tests

## Next Steps

### Required Implementation: B Cookie Acquisition

Please provide implementation details for converting validated JWT tokens to `test_orders-portal` cookies:

1. **Method**: How should B convert JWT → Cookie?
2. **API Calls**: Should B call specific endpoints?
3. **Token Construction**: Should B construct cookies from JWT claims?
4. **Intermediate Steps**: Are additional authentication steps required?

### Questions for Implementation

1. Should the MCP Server call the permission service API to exchange JWT for cookie?
2. Should the MCP Server construct the cookie directly from JWT claims?
3. Are there specific user session management requirements?
4. How should cookie expiration be handled relative to JWT expiration?

## Security Considerations

1. **JWT Validation**: Always validate JWT signature, expiration, and claims
2. **Cookie Security**: Ensure cookies are properly formatted and not expired
3. **Token Refresh**: Handle token expiration and refresh scenarios
4. **Error Handling**: Provide clear error messages for authentication failures
5. **Logging**: Log authentication events for security monitoring

## Testing

### Test Commands
```bash
npm run test:connection-auth    # Test connection authentication
npm run test:auth              # Test cookie authentication
npm run test:quick             # Test basic functionality
```

### Manual Testing
```bash
# Test with OAuth2 token
curl -X POST http://localhost:6300/mcp \
  -H "Authorization: Bearer <jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"initialize",...}'

# Test with cookie
curl -X POST http://localhost:6300/mcp \
  -H "Cookie: test_orders-portal=<cookie-value>" \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"initialize",...}'
```
