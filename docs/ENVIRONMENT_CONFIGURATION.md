# 🔧 Environment Configuration Guide

This guide explains where and how to configure environment variables for the MCP server in different deployment scenarios.

## 📋 Configuration Hierarchy

Environment variables are loaded in this order (later values override earlier ones):

1. **Dockerfile defaults** (only non-sensitive values)
2. **`.env` file** (local development)
3. **docker-compose environment** (container orchestration)
4. **System environment variables** (runtime)
5. **Secrets management** (production)

## 🏠 Local Development

### Using `.env` file (Recommended)

```bash
# Copy template
cp .env.example .env

# Edit with your values
nano .env
```

**Example `.env` for development:**
```env
NODE_ENV=development
TRANSPORT=stdio
MCP_SERVER_PORT=3000

# API Configuration
VITE_API_HOST=https://fe-dev-i.ingka-dt.cn
VITE_API_HOST_KONG=https://api-dev-mpp-fe.ingka-dt.cn

# Development - OAuth2 disabled, use cookies
OAUTH2_ENABLED=false
AUTH_COOKIES=your-extracted-cookie-here

# Debug enabled
DEBUG_SERVICE_ADAPTER=true
CONNECTION_AUTH_ENABLED=false
```

### Using npm scripts

```bash
# Set environment variables inline
OAUTH2_ENABLED=false npm run dev

# Or export them
export OAUTH2_ENABLED=false
export DEBUG_SERVICE_ADAPTER=true
npm run dev
```

## 🐳 Docker Development

### Using docker-compose with .env

```bash
# docker-compose automatically loads .env file
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

### Override specific variables

```bash
# Override via command line
OAUTH2_ENABLED=false docker-compose up

# Or create .env.dev
cp .env.example .env.dev
# Edit .env.dev
docker-compose --env-file .env.dev up
```

## 🚀 Production Deployment

### Option 1: Environment Variables (Recommended)

```bash
# Set environment variables before running
export NODE_ENV=production
export MCP_SERVER_BASE_URL=https://mcp.ingka-internal.cn
export OAUTH2_CLIENT_SECRET=your-secret-here
export VITE_MASTER_DATA_API_KEY=your-api-key

# Run container
docker run -d \
  -e NODE_ENV \
  -e MCP_SERVER_BASE_URL \
  -e OAUTH2_CLIENT_SECRET \
  -e VITE_MASTER_DATA_API_KEY \
  orders-portal-mcp-server
```

### Option 2: Production .env file

```bash
# Create production .env (keep secure!)
cat > .env.prod << EOF
NODE_ENV=production
MCP_SERVER_BASE_URL=https://mcp.ingka-internal.cn
OAUTH2_ENABLED=true
OAUTH2_CLIENT_SECRET=your-secret-here
VITE_MASTER_DATA_API_KEY=your-api-key
CONNECTION_AUTH_ENABLED=true
CONNECTION_AUTH_STRICT=true
EOF

# Use with docker-compose
docker-compose --env-file .env.prod -f docker-compose.yml -f docker-compose.prod.yml up
```

### Option 3: Docker Secrets (Most Secure)

```bash
# Create secrets
echo "your-oauth2-secret" | docker secret create oauth2_client_secret -
echo "your-api-key" | docker secret create master_data_api_key -

# Use in docker-compose.yml
version: '3.8'
services:
  mcp-server:
    secrets:
      - oauth2_client_secret
      - master_data_api_key
    environment:
      OAUTH2_CLIENT_SECRET_FILE: /run/secrets/oauth2_client_secret
      VITE_MASTER_DATA_API_KEY_FILE: /run/secrets/master_data_api_key

secrets:
  oauth2_client_secret:
    external: true
  master_data_api_key:
    external: true
```

## ☁️ Cloud Deployment

### Kubernetes

```yaml
# configmap.yaml - Non-sensitive config
apiVersion: v1
kind: ConfigMap
metadata:
  name: mcp-server-config
data:
  NODE_ENV: "production"
  TRANSPORT: "http"
  MCP_SERVER_PORT: "3000"
  OAUTH2_ENABLED: "true"
  CONNECTION_AUTH_ENABLED: "true"

---
# secret.yaml - Sensitive config
apiVersion: v1
kind: Secret
metadata:
  name: mcp-server-secrets
type: Opaque
stringData:
  OAUTH2_CLIENT_SECRET: "your-secret-here"
  VITE_MASTER_DATA_API_KEY: "your-api-key"
  AUTH_COOKIES: "your-cookies-here"

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-server
spec:
  template:
    spec:
      containers:
      - name: mcp-server
        image: orders-portal-mcp-server:latest
        envFrom:
        - configMapRef:
            name: mcp-server-config
        - secretRef:
            name: mcp-server-secrets
```

### AWS ECS

```json
{
  "taskDefinition": {
    "containerDefinitions": [
      {
        "name": "mcp-server",
        "image": "orders-portal-mcp-server:latest",
        "environment": [
          {"name": "NODE_ENV", "value": "production"},
          {"name": "TRANSPORT", "value": "http"},
          {"name": "MCP_SERVER_PORT", "value": "3000"}
        ],
        "secrets": [
          {
            "name": "OAUTH2_CLIENT_SECRET",
            "valueFrom": "arn:aws:secretsmanager:region:account:secret:oauth2-secret"
          },
          {
            "name": "VITE_MASTER_DATA_API_KEY",
            "valueFrom": "arn:aws:secretsmanager:region:account:secret:api-key"
          }
        ]
      }
    ]
  }
}
```

## 🔒 Security Best Practices

### ❌ Never Do This

```dockerfile
# DON'T hardcode secrets in Dockerfile
ENV OAUTH2_CLIENT_SECRET=secret123
ENV VITE_MASTER_DATA_API_KEY=key456
```

```bash
# DON'T commit .env files with secrets
git add .env  # ❌ NEVER!
```

### ✅ Do This Instead

```dockerfile
# Only non-sensitive defaults in Dockerfile
ENV NODE_ENV=production
ENV TRANSPORT=http
ENV MCP_SERVER_PORT=3000
```

```bash
# Use .env.example as template
cp .env.example .env
# Add .env to .gitignore
echo ".env" >> .gitignore
```

## 📊 Environment Variable Categories

### 🔓 Non-Sensitive (OK in Dockerfile/Git)
- `NODE_ENV`
- `TRANSPORT`
- `MCP_SERVER_PORT`
- `OAUTH2_ENABLED`
- `CONNECTION_AUTH_ENABLED`
- `DEBUG_SERVICE_ADAPTER`

### 🔐 Sensitive (Never in Dockerfile/Git)
- `OAUTH2_CLIENT_SECRET`
- `VITE_MASTER_DATA_API_KEY`
- `AUTH_COOKIES`

### 🏢 Environment-Specific (Configure per deployment)
- `MCP_SERVER_BASE_URL`
- `VITE_API_HOST`
- `VITE_API_HOST_KONG`
- `KEYCLOAK_BASE_URL`
- `OAUTH2_REDIRECT_URI`

## 🛠️ Configuration Validation

Your MCP server validates required environment variables on startup:

```typescript
// Required when OAUTH2_ENABLED=true
if (OAUTH2_ENABLED && !OAUTH2_CLIENT_SECRET) {
  throw new Error('OAUTH2_CLIENT_SECRET is required when OAuth2 is enabled');
}

// Required when OAUTH2_ENABLED=false
if (!OAUTH2_ENABLED && !AUTH_COOKIES) {
  throw new Error('AUTH_COOKIES is required when OAuth2 is disabled');
}

// Always required
if (!VITE_API_HOST_KONG) {
  throw new Error('VITE_API_HOST_KONG is required');
}
```

## 🔍 Debugging Configuration

### Check loaded environment variables

```bash
# In development
npm run dev -- --debug-env

# In Docker container
docker exec mcp-server node -e "console.log(process.env)"

# Check specific variables
docker exec mcp-server printenv | grep OAUTH2
```

### Validate configuration

```bash
# Test configuration endpoint
curl http://localhost:3000/health

# Check authentication status
curl http://localhost:3000/auth/status
```

## 📝 Configuration Templates

See these files for complete configuration examples:

- **`.env.example`** - Complete template with all variables
- **`docker-compose.yml`** - Docker environment configuration
- **`docker-compose.prod.yml`** - Production overrides
- **`docker-compose.dev.yml`** - Development overrides

## 🆘 Troubleshooting

### Common Issues

1. **Missing required variables**: Check startup logs for validation errors
2. **Wrong environment file**: Ensure correct `.env` file is loaded
3. **Docker not loading .env**: Verify file is in same directory as docker-compose.yml
4. **Secrets not accessible**: Check file permissions and secret mounting

### Debug Commands

```bash
# Check what docker-compose will use
docker-compose config

# Validate environment loading
docker-compose run --rm mcp-server printenv

# Test with minimal config
docker run --rm -e NODE_ENV=development orders-portal-mcp-server node -e "console.log('OK')"
```
