# 🐳 Multi-stage Dockerfile for Orders Portal MCP Server
# Optimized for production deployment with security best practices

# ==================== BUILD STAGE ====================
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies (including dev dependencies for build)
RUN npm ci --include=dev

# Copy source code
COPY src/ ./src/

# Build the application
RUN npm run build

# Verify build output
RUN ls -la dist/ && test -f dist/index.js

# ==================== PRODUCTION STAGE ====================
FROM node:20-alpine AS production

# Create non-root user for security
RUN addgroup -g 1001 -S mcpuser && \
    adduser -S mcpuser -u 1001 -G mcpuser

# Set working directory
WORKDIR /app

# Install production dependencies only
COPY package*.json ./
RUN npm ci --only=production && \
    npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Copy any additional runtime files if needed
# COPY docs/ ./docs/
# COPY scripts/ ./scripts/

# Create logs directory with proper permissions
RUN mkdir -p logs && \
    chown -R mcpuser:mcpuser /app

# Switch to non-root user
USER mcpuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "fetch('http://localhost:${MCP_SERVER_PORT:-3000}/health').then(r=>r.ok?process.exit(0):process.exit(1)).catch(()=>process.exit(1))"

# Expose port (configurable via environment)
EXPOSE ${MCP_SERVER_PORT:-3000}

# Environment variables with defaults
ENV NODE_ENV=production \
    MCP_SERVER_PORT=3000 \
    TRANSPORT=http \
    OAUTH2_ENABLED=true \
    CONNECTION_AUTH_ENABLED=true \
    CONNECTION_AUTH_STRICT=true

# Labels for metadata
LABEL maintainer="IKEA Orders Portal Team" \
      version="0.1.0" \
      description="Orders Portal MCP Server" \
      org.opencontainers.image.title="Orders Portal MCP Server" \
      org.opencontainers.image.description="Model Context Protocol server for IKEA Orders Portal" \
      org.opencontainers.image.version="0.1.0" \
      org.opencontainers.image.vendor="IKEA" \
      org.opencontainers.image.licenses="MIT"

# Start the application
CMD ["node", "dist/index.js"]
