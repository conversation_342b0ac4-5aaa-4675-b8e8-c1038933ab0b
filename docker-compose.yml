# 🐳 Docker Compose for MCP Server ODI
# Production-ready configuration with environment-specific overrides

version: '3.8'

services:
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: mcp-server-odi:latest
    container_name: mcp-server-odi
    
    # Port mapping (configurable via environment)
    ports:
      - "${MCP_SERVER_PORT:-3000}:${MCP_SERVER_PORT:-3000}"
    
    # Environment variables
    environment:
      # Application Configuration
      NODE_ENV: ${NODE_ENV:-production}
      TRANSPORT: ${TRANSPORT:-http}

      # Server Configuration
      MCP_SERVER_PORT: ${MCP_SERVER_PORT:-3000}
      MCP_SERVER_BASE_URL: ${MCP_SERVER_BASE_URL:-http://localhost:3000}

      # API Configuration
      VITE_API_HOST: ${VITE_API_HOST:-https://fe-dev-i.ingka-dt.cn}
      VITE_API_HOST_KONG: ${VITE_API_HOST_KONG:-https://api-dev-mpp-fe.ingka-dt.cn}
      VITE_MASTER_DATA_API_HOST: ${VITE_MASTER_DATA_API_HOST:-https://master-data-api-dev.ingka-dt.cn}
      VITE_MASTER_DATA_API_KEY: ${VITE_MASTER_DATA_API_KEY:-}

      # Authentication Configuration
      AUTH_COOKIES: ${AUTH_COOKIES:-}
      X_CUSTOM_REFERRER: ${X_CUSTOM_REFERRER:-https://fe-dev-i.ingka-dt.cn/order-web}

      # OAuth2 Configuration
      OAUTH2_ENABLED: ${OAUTH2_ENABLED:-true}
      KEYCLOAK_BASE_URL: ${KEYCLOAK_BASE_URL:-https://keycloak.ingka-dt.cn}
      KEYCLOAK_REALM: ${KEYCLOAK_REALM:-master}
      OAUTH2_CLIENT_ID: ${OAUTH2_CLIENT_ID:-mcp-server}
      OAUTH2_CLIENT_SECRET: ${OAUTH2_CLIENT_SECRET:-}
      OAUTH2_REDIRECT_URI: ${OAUTH2_REDIRECT_URI:-}
      OAUTH2_SCOPES: ${OAUTH2_SCOPES:-openid,profile,email}

      # Connection Authentication
      CONNECTION_AUTH_ENABLED: ${CONNECTION_AUTH_ENABLED:-true}
      CONNECTION_AUTH_STRICT: ${CONNECTION_AUTH_STRICT:-true}
      CONNECTION_AUTH_TEST_API: ${CONNECTION_AUTH_TEST_API:-false}

      # Debug Configuration
      DEBUG_SERVICE_ADAPTER: ${DEBUG_SERVICE_ADAPTER:-false}

      # Per-Request Authentication (Optional)
      PER_REQUEST_AUTH_ENABLED: ${PER_REQUEST_AUTH_ENABLED:-false}
      PER_REQUEST_AUTH_CACHE_ENABLED: ${PER_REQUEST_AUTH_CACHE_ENABLED:-true}
      PER_REQUEST_AUTH_CACHE_MAX_AGE: ${PER_REQUEST_AUTH_CACHE_MAX_AGE:-300}
      PER_REQUEST_AUTH_LOG_VALIDATION: ${PER_REQUEST_AUTH_LOG_VALIDATION:-false}
    
    # Health check
    healthcheck:
      test: ["CMD", "node", "-e", "fetch('http://localhost:${MCP_SERVER_PORT:-3000}/health').then(r=>r.ok?process.exit(0):process.exit(1)).catch(()=>process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
    
    # Restart policy
    restart: unless-stopped
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Volume for logs (optional)
    volumes:
      - ./logs:/app/logs:rw
    
    # Network configuration
    networks:
      - mcp-network

# Networks
networks:
  mcp-network:
    driver: bridge
    name: mcp-server-odi-network

# Volumes (if needed for persistent data)
volumes:
  mcp-logs:
    driver: local
