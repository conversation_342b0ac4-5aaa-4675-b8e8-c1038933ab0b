# 🚀 Production Docker Compose Override
# Enhanced configuration for production deployment

version: '3.8'

services:
  mcp-server:
    # Production image (can be pulled from registry)
    # image: your-registry.com/mcp-server-odi:${VERSION:-latest}
    
    # Production environment overrides
    environment:
      NODE_ENV: production
      
      # Enhanced security settings
      CONNECTION_AUTH_ENABLED: true
      CONNECTION_AUTH_STRICT: true
      CONNECTION_AUTH_TEST_API: false
      
      # Disable debug features
      DEBUG_SERVICE_ADAPTER: false
      
      # Production OAuth2 settings
      OAUTH2_ENABLED: true
    
    # Production resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 256M
      
      # Production restart policy
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    
    # Enhanced health check for production
    healthcheck:
      test: ["CMD", "node", "-e", "fetch('http://localhost:${MCP_SERVER_PORT:-3000}/health').then(r=>r.ok?process.exit(0):process.exit(1)).catch(()=>process.exit(1))"]
      interval: 15s
      timeout: 5s
      retries: 5
      start_period: 60s
    
    # Production logging with structured format
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=mcp-server,environment=production"
    
    # Security options
    security_opt:
      - no-new-privileges:true
    
    # Read-only root filesystem (if your app supports it)
    # read_only: true
    
    # Temporary filesystems for writable directories
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    
    # User namespace remapping (if supported by host)
    # user: "1001:1001"

# Production network with custom configuration
networks:
  mcp-network:
    driver: bridge
    name: mcp-server-odi-production
    ipam:
      config:
        - subnet: **********/16
