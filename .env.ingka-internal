# ================================
# Orders Portal MCP Server - INGKA-INTERNAL Environment
# ================================

# Environment
NODE_ENV=production

# API Endpoints (Required)
VITE_API_HOST=https://admin.ingka-internal.cn/app-api/orders-portal
VITE_API_HOST_KONG=https://mpp-fe-i.ingka-internal.cn/order-web
VITE_MASTER_DATA_API_HOST=https://admin.ingka-internal.cn/master-data
VITE_MASTER_DATA_API_KEY=v6KqKzWd8EnJD00pgrcX

# Authentication (Required when OAuth2 disabled)
AUTH_COOKIES=orders-portal=NDIzMTAwZTgtMxxxxxxxxxxxx
X_CUSTOM_REFERRER=https://admin.ingka-internal.cn/app/orders-portal/oms/index

# Transport & Debug
TRANSPORT=stdio
DEBUG_SERVICE_ADAPTER=true

# MCP Server Port
MCP_SERVER_PORT=3000

# ================================
# OAuth2 Configuration
# ================================
OAUTH2_ENABLED=true
KEYCLOAK_BASE_URL=https://keycloak.ingka-internal.cn
KEYCLOAK_REALM=master
OAUTH2_CLIENT_ID=mcp-server-client
OAUTH2_CLIENT_SECRET=JHULZ
OAUTH2_SCOPES=openid,profile,email